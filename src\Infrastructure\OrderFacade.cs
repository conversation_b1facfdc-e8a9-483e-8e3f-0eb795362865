﻿using Amazon.Runtime.Internal;
using commercetools.Sdk.Api.Serialization;
using Confluent.Kafka;
using Elastic.Apm;
using Elastic.Apm.Api;
using FS.Keycloak.RestApiClient.Model;
using IT.Microservices.OrderReactor.Domain;
using ITF.SharedLibraries.Alerting;
using ITF.SharedLibraries.ExtensionMethods;
using ITF.SharedLibraries.FeatureFlags;
using ITF.SharedLibraries.Polly;
using Microsoft.Azure.Amqp.Framing;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using static ITF.Lib.Common.Notifications.Tracing;
using static ITF.SharedLibraries.ElasticSearch.APM.CorrelationLogsHelper;
using static ITF.SharedLibraries.ExtensionMethods.Serializer;
using static ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;

namespace IT.Microservices.OrderReactor.Infrastructure
{
    public class OrderFacade(
        IOrderUseCase orderUseCase,
        IFeatureFlags featureManager,
        SerializerService serializerService,
        IOrderReactorIdempotenceRepository orderReactorIdempotenceRepository,
        ILogger<OrderFacade> logger , ISlackAlertService slackAlertService) : IOrderFacade
    {
        private async Task KafkaOrderCreatedUseCaseSpecificProcess(LegacyOrderCreatedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderCreatedUseCaseSpecificProcess)}_{nameof(LegacyOrderCreatedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.GetMessageKey());
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderCreatedUseCaseSpecificProcess), nameof(LegacyOrderCreatedMessage), message?.GetMessageKey());

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderAssignedUseCaseSpecificProcess(LegacyOrderAssignedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderAssignedUseCaseSpecificProcess)}_{nameof(LegacyOrderAssignedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);
                  //TagTransaction(t, "Identifier", orderAssignedDTO.OrderIdentifier);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderAssignedUseCaseSpecificProcess), nameof(LegacyOrderAssignedMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderCancelledUseCaseSpecificProcess(LegacyOrderCancelledMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderCancelledUseCaseSpecificProcess)}_{nameof(LegacyOrderCancelledMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderCancelledUseCaseSpecificProcess), nameof(LegacyOrderCancelledMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderDeliveryTimeUpdatedUseCaseSpecificProcess(LegacyOrderDeliveryTimeUpdatedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderDeliveryTimeUpdatedUseCaseSpecificProcess)}_{nameof(LegacyOrderDeliveryTimeUpdatedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderDeliveryTimeUpdatedUseCaseSpecificProcess), nameof(LegacyOrderDeliveryTimeUpdatedMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderDeliveryStatusUpdatedUseCaseSpecificProcess(LegacyOrderDeliveryStatusUpdatedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderDeliveryStatusUpdatedUseCaseSpecificProcess)}_{nameof(LegacyOrderDeliveryStatusUpdatedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderDeliveryStatusUpdatedUseCaseSpecificProcess), nameof(LegacyOrderDeliveryStatusUpdatedMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderDeliveryCostUpdatedUseCaseSpecificProcess(LegacyOrderDeliveryCostUpdatedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderDeliveryCostUpdatedUseCaseSpecificProcess)}_{nameof(LegacyOrderDeliveryCostUpdatedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderDeliveryCostUpdatedUseCaseSpecificProcess), nameof(LegacyOrderDeliveryCostUpdatedMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderDeliveryDateUpdatedUseCaseSpecificProcess(LegacyOrderDeliveryDateUpdatedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderDeliveryDateUpdatedUseCaseSpecificProcess)}_{nameof(LegacyOrderDeliveryDateUpdatedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderDeliveryDateUpdatedUseCaseSpecificProcess), nameof(LegacyOrderDeliveryDateUpdatedMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderCardMessageUpdatedUseCaseSpecificProcess(LegacyOrderCardMessageUpdatedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderCardMessageUpdatedUseCaseSpecificProcess)}_{nameof(LegacyOrderCardMessageUpdatedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderCardMessageUpdatedUseCaseSpecificProcess), nameof(LegacyOrderCardMessageUpdatedMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderNotesUpdatedUseCaseSpecificProcess(LegacyOrderNotesUpdatedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderNotesUpdatedUseCaseSpecificProcess)}_{nameof(LegacyOrderNotesUpdatedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderNotesUpdatedUseCaseSpecificProcess), nameof(LegacyOrderNotesUpdatedMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderRecipientCoordinatesUpdatedUseCaseSpecificProcess(LegacyOrderRecipientCoordinatesUpdatedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderRecipientCoordinatesUpdatedUseCaseSpecificProcess)}_{nameof(LegacyOrderRecipientCoordinatesUpdatedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "OrderLatitude", message?.Payload?.Latitude.ToString());
                  TagTransaction(t, "OrderLongitude", message?.Payload?.Longitude.ToString());
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)                 
                  logger.LogInformation("Process {process} on message {message} for order {OrderNumber} latitude {Latitude} longitude {Longitude}", nameof(KafkaOrderRecipientCoordinatesUpdatedUseCaseSpecificProcess), nameof(LegacyOrderRecipientCoordinatesUpdatedMessage), message?.Payload?.OrderIdentifier, message?.Payload?.Latitude, message?.Payload?.Longitude);
                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderDeliveryAddressUpdatedUseCaseSpecificProcess(LegacyOrderDeliveryAddressUpdatedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderDeliveryAddressUpdatedUseCaseSpecificProcess)}_{nameof(LegacyOrderDeliveryAddressUpdatedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderDeliveryAddressUpdatedUseCaseSpecificProcess), nameof(LegacyOrderDeliveryAddressUpdatedMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderDeliveredOnBehalOfAFloristUseCaseSpecificProcess(LegacyOrderDeliveredOnBehalfMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderDeliveredOnBehalOfAFloristUseCaseSpecificProcess)}_{nameof(LegacyOrderDeliveredOnBehalfMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderDeliveredOnBehalOfAFloristUseCaseSpecificProcess), nameof(LegacyOrderDeliveredOnBehalfMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderAcceptedUseCaseSpecificProcess(LegacyOrderAcceptedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderAcceptedUseCaseSpecificProcess)}_{nameof(LegacyOrderAcceptedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderAcceptedUseCaseSpecificProcess), nameof(LegacyOrderAcceptedMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderRejectedUseCaseSpecificProcess(LegacyOrderRejectedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderRejectedUseCaseSpecificProcess)}_{nameof(LegacyOrderRejectedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderRejectedUseCaseSpecificProcess), nameof(LegacyOrderRejectedMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderDeliveredUseCaseSpecificProcess(LegacyOrderDeliveredMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderDeliveredUseCaseSpecificProcess)}_{nameof(LegacyOrderDeliveredMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderDeliveredUseCaseSpecificProcess), nameof(LegacyOrderDeliveredMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderItemUpdatedUseCaseSpecificProcess(LegacyOrderItemUpdatedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderItemUpdatedUseCaseSpecificProcess)}_{nameof(LegacyOrderItemUpdatedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderItemUpdatedUseCaseSpecificProcess), nameof(LegacyOrderItemUpdatedMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderItemExecutorAmountUpdatedUseCaseSpecificProcess(LegacyOrderItemExecutorAmountUpdatedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderItemExecutorAmountUpdatedUseCaseSpecificProcess)}_{nameof(LegacyOrderItemExecutorAmountUpdatedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderItemExecutorAmountUpdatedUseCaseSpecificProcess), nameof(LegacyOrderItemExecutorAmountUpdatedMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderAssignationRemovedUseCaseSpecificProcess(LegacyOrderAssignationRemovedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderAssignationRemovedUseCaseSpecificProcess)}_{nameof(LegacyOrderAssignationRemovedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderAssignationRemovedUseCaseSpecificProcess), nameof(LegacyOrderAssignationRemovedMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderAcceptedOnBehalOfAFloristUseCaseSpecificProcess(LegacyOrderAcceptedOnBehalfMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderAcceptedOnBehalOfAFloristUseCaseSpecificProcess)}_{nameof(LegacyOrderAcceptedOnBehalfMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderAcceptedOnBehalOfAFloristUseCaseSpecificProcess), nameof(LegacyOrderAcceptedOnBehalfMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaOrderRejectedOnBehalOfAFloristUseCaseSpecificProcess(LegacyOrderRejectedOnBehalfMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderRejectedOnBehalOfAFloristUseCaseSpecificProcess)}_{nameof(LegacyOrderRejectedOnBehalfMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Check any feature flag if relevant
                  //if (!await _featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
                  //{
                  //    _logger.LogWarning("Feature XXX is disabled, process skipped");
                  //    return;
                  //}

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderRejectedOnBehalOfAFloristUseCaseSpecificProcess), nameof(LegacyOrderRejectedOnBehalfMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }
        private async Task KafkaOrderSentUseCaseSpecificProcess(LegacyOrderSentMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderSentUseCaseSpecificProcess)}_{nameof(LegacyOrderSentMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderSentUseCaseSpecificProcess), nameof(LegacyOrderSentMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }
        private async Task KafkaOrderRecipientNameUpdatedUseCaseSpecificProcess(LegacyOrderRecipientNameUpdatedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderRecipientNameUpdatedUseCaseSpecificProcess)}_{nameof(LegacyOrderRecipientNameUpdatedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderRecipientNameUpdatedUseCaseSpecificProcess), nameof(LegacyOrderRecipientNameUpdatedMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }
        private async Task KafkaOrderRecipientPhoneNumberUpdatedUseCaseSpecificProcess(LegacyOrderRecipientPhoneNumberUpdatedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaOrderRecipientPhoneNumberUpdatedUseCaseSpecificProcess)}_{nameof(LegacyOrderRecipientPhoneNumberUpdatedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderIdentifier);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaOrderRecipientPhoneNumberUpdatedUseCaseSpecificProcess), nameof(LegacyOrderRecipientPhoneNumberUpdatedMessage), message?.Payload?.OrderIdentifier);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        #region RAO Legacy FR Messages

        private async Task KafkaRAOLegacyOrderPlacedUseCaseSpecificProcess(OrderPlacedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaRAOLegacyOrderPlacedUseCaseSpecificProcess)}_{nameof(OrderPlacedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderId);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaRAOLegacyOrderPlacedUseCaseSpecificProcess), nameof(OrderPlacedMessage), message?.Payload?.OrderId);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }
        private async Task KafkaRAOLegacyOrderAssignmentUseCaseSpecificProcess(OrderAssignmentMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaRAOLegacyOrderAssignmentUseCaseSpecificProcess)}_{nameof(OrderAssignmentMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderId);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaRAOLegacyOrderAssignmentUseCaseSpecificProcess), nameof(OrderAssignmentMessage), message?.Payload?.OrderId);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaRAOLegacyOrderUpdatedUseCaseSpecificProcess(OrderUpdatedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaRAOLegacyOrderUpdatedUseCaseSpecificProcess)}_{nameof(OrderUpdatedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderId);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaRAOLegacyOrderUpdatedUseCaseSpecificProcess), nameof(OrderUpdatedMessage), message?.Payload?.OrderId);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaRAOLegacyOrderManagementStatusUseCaseSpecificProcess(OrderManagementStatusMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaRAOLegacyOrderManagementStatusUseCaseSpecificProcess)}_{nameof(OrderManagementStatusMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderId);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaRAOLegacyOrderManagementStatusUseCaseSpecificProcess), nameof(OrderManagementStatusMessage), message?.Payload?.OrderId);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaRAOLegacyOrderDeliveryCourierUpdatedUseCaseSpecificProcess(OrderDeliveryCourierUpdatedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaRAOLegacyOrderDeliveryCourierUpdatedUseCaseSpecificProcess)}_{nameof(OrderDeliveryCourierUpdatedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderId);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaRAOLegacyOrderDeliveryCourierUpdatedUseCaseSpecificProcess), nameof(OrderDeliveryCourierUpdatedMessage), message?.Payload?.OrderId);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaRAOLegacOrderDeliveryCourierResetedUseCaseSpecificProcess(OrderDeliveryCourierResetedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaRAOLegacOrderDeliveryCourierResetedUseCaseSpecificProcess)}_{nameof(OrderDeliveryCourierResetedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderId);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaRAOLegacOrderDeliveryCourierResetedUseCaseSpecificProcess), nameof(OrderDeliveryCourierResetedMessage), message?.Payload?.OrderId);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaRAOLegacyOrderDeliveryCourierInitializedUseCaseSpecificProcess(OrderDeliveryCourierInitializedMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaRAOLegacyOrderDeliveryCourierInitializedUseCaseSpecificProcess)}_{nameof(OrderDeliveryCourierInitializedMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderId);
                  TagTransaction(t, "CausationId", message.CausationId);

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference}", nameof(KafkaRAOLegacyOrderDeliveryCourierInitializedUseCaseSpecificProcess), nameof(OrderDeliveryCourierInitializedMessage), message?.Payload?.OrderId);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        private async Task KafkaRAOLegacyInvoiceMessageUseCaseSpecificProcess(InvoiceMessage message, string topic = null, int? partition = null, long? offset = null)
        {
            // Do your tracing / tagging / logging / retry policy / feature flag checks here
            await Agent.Tracer
              .CaptureTransaction($"{nameof(KafkaRAOLegacyInvoiceMessageUseCaseSpecificProcess)}_{nameof(InvoiceMessage)}Transaction", "Kafka", async (t) =>
              {
                  // Tag the transaction with relevant properties
                  TagTransaction(t, "Topic", topic);
                  TagTransaction(t, "Partition", partition);
                  TagTransaction(t, "Offset", offset);
                  TagTransaction(t, "OrderReference", message?.Payload?.OrderId);
                  TagTransaction(t, "FloristId", message?.Payload?.FloristId);
                  TagTransaction(t, "CausationId", message?.CausationId);

                  // Make some standard log (on typed objects)
                  logger.LogInformation("Process {process} on message {message} for order reference {orderReference} and FloristId {floristId}", nameof(KafkaRAOLegacyInvoiceMessageUseCaseSpecificProcess), nameof(InvoiceMessage), message?.Payload?.OrderId , message?.Payload?.FloristId);

                  // Apply a polly policy to your underlying process if required
                  await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
                  {
                      await orderUseCase.SychronizeProcess(message);
                  });
              });
        }

        #endregion

        public async Task Process(object data, string topic = null, int? partition = null, long? offset = null)
        {
            try
            {
                try
                {
                    await CheckIdempotence(data);
                }
                catch (OrderReactorIdempotenceAlreadyExistingException) // just here to grab the error and skip the message and read the next one
                {
                    return;
                }

                var result = data switch
                {
                    LegacyOrderCreatedMessage msg => KafkaOrderCreatedUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderAssignedMessage msg => KafkaOrderAssignedUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderCancelledMessage msg => KafkaOrderCancelledUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderDeliveryTimeUpdatedMessage msg => KafkaOrderDeliveryTimeUpdatedUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderDeliveryStatusUpdatedMessage msg => KafkaOrderDeliveryStatusUpdatedUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderDeliveryCostUpdatedMessage msg => KafkaOrderDeliveryCostUpdatedUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderDeliveryDateUpdatedMessage msg => KafkaOrderDeliveryDateUpdatedUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderCardMessageUpdatedMessage msg => KafkaOrderCardMessageUpdatedUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderNotesUpdatedMessage msg => KafkaOrderNotesUpdatedUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderRecipientCoordinatesUpdatedMessage msg => KafkaOrderRecipientCoordinatesUpdatedUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderDeliveryAddressUpdatedMessage msg => KafkaOrderDeliveryAddressUpdatedUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderDeliveredOnBehalfMessage msg => KafkaOrderDeliveredOnBehalOfAFloristUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderAcceptedMessage msg => KafkaOrderAcceptedUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderRejectedMessage msg => KafkaOrderRejectedUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderDeliveredMessage msg => KafkaOrderDeliveredUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderAssignationRemovedMessage msg => KafkaOrderAssignationRemovedUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderAcceptedOnBehalfMessage msg => KafkaOrderAcceptedOnBehalOfAFloristUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderRejectedOnBehalfMessage msg => KafkaOrderRejectedOnBehalOfAFloristUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderSentMessage msg => KafkaOrderSentUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderRecipientNameUpdatedMessage msg => KafkaOrderRecipientNameUpdatedUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderRecipientPhoneNumberUpdatedMessage msg => KafkaOrderRecipientPhoneNumberUpdatedUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderItemUpdatedMessage msg => KafkaOrderItemUpdatedUseCaseSpecificProcess(msg, topic, partition, offset),
                    LegacyOrderItemExecutorAmountUpdatedMessage msg => KafkaOrderItemExecutorAmountUpdatedUseCaseSpecificProcess(msg, topic, partition, offset),

                    // France specific part
                    OrderPlacedMessage msg => KafkaRAOLegacyOrderPlacedUseCaseSpecificProcess(msg, topic, partition, offset),
                    OrderAssignmentMessage msg => KafkaRAOLegacyOrderAssignmentUseCaseSpecificProcess(msg, topic, partition, offset),
                    OrderUpdatedMessage msg => KafkaRAOLegacyOrderUpdatedUseCaseSpecificProcess(msg, topic, partition, offset),
                    OrderManagementStatusMessage msg => KafkaRAOLegacyOrderManagementStatusUseCaseSpecificProcess(msg, topic, partition, offset),
                    OrderDeliveryCourierUpdatedMessage msg => KafkaRAOLegacyOrderDeliveryCourierUpdatedUseCaseSpecificProcess(msg, topic, partition, offset),
                    OrderDeliveryCourierResetedMessage msg => KafkaRAOLegacOrderDeliveryCourierResetedUseCaseSpecificProcess(msg, topic, partition, offset),
                    OrderDeliveryCourierInitializedMessage msg => KafkaRAOLegacyOrderDeliveryCourierInitializedUseCaseSpecificProcess(msg, topic, partition, offset),
                    InvoiceMessage msg => KafkaRAOLegacyInvoiceMessageUseCaseSpecificProcess(msg, topic, partition, offset),


                    _ => Task.CompletedTask
                };

                await result;

                // add the current message into the idempotence repository
                try
                {
                    await orderReactorIdempotenceRepository.ReplaceOneAsync(new((data as dynamic).MessageId));
                }
                catch (Exception e)
                {
                    logger.LogError("Fail to add the message into the idempotence repository, see exception : {ex} and raw message content : {mess}", e.ToString(), data?.Serialize());
                }
                
            }
            catch (Exception e)
            {
                var stopHandler = false;
                await Agent.Tracer
                .CaptureTransaction($"ERR_KafkaExceptionHandlingTransaction", "Kafka", async (t) =>
                {
                    TagTransaction(t, "Topic", topic);
                    TagTransaction(t, "Partition", partition);
                    TagTransaction(t, "Offset", offset);
                    var (orderReference, category, causationId) = GetInformations(data);
                    TagTransaction(t, "OrderReference", orderReference);
                    TagTransaction(t, "CausationId", causationId);

                    logger.LogError(e, "Failed to apply {eventType} event with {payload} for order reference {orderReference} from Kafka with exception message : {error}", data?.GetType()?.Name, data?.Serialize(SerializerType.CommerceTools, serializerService), orderReference, e.Message);

                    await (slackAlertService?.SendErrorAlertAsync($"Main OrderFacade Process Error : Failed to apply {data?.GetType()?.Name} event with {data?.Serialize(SerializerType.CommerceTools, serializerService)} for order reference {orderReference} from Kafka with exception message : {e}") ?? Task.CompletedTask);

                    stopHandler = await NeedShutdownAsync(e);

                    if (stopHandler)
                        logger.LogCritical(e, "Handler stopped !");

                }, DistributedTracingData.TryDeserializeFromString(GetTracingData(data)));

                if (stopHandler)
                {
                    await Task.Delay(5000); // To flush APM transactions
                    throw;
                }
            }
        }

        private async Task CheckIdempotence(object data)
        {
            try
            {
                dynamic kafkaMessage = data;
                string messageId = kafkaMessage.MessageId;
                var alreadyProcessedMessage = await orderReactorIdempotenceRepository.FindByIdAsync(messageId);

                if (alreadyProcessedMessage != null)
                    await Agent.Tracer
                       .CaptureTransaction($"ERR_IdempotenceMessageAlreadyExistTransaction", "Kafka", async (t) =>
                       {
                           TagTransaction(t, "MessageId", messageId);
                           TagTransaction(t, "MessageKey", kafkaMessage.GetMessageKey() as string);

                           logger.LogError("Message with ID {messageId} has already been processed for MessageKey : {key}  / SKIP THE MESSAGE", messageId, kafkaMessage.GetMessageKey() as string);

                           await (slackAlertService?.SendErrorAlertAsync($"CheckIdempotence Error : Message with ID {messageId} has already been processed for MessageKey : {kafkaMessage.GetMessageKey() as string}") ?? Task.CompletedTask);

                           throw new OrderReactorIdempotenceAlreadyExistingException($"Message with ID {messageId} has already been processed for MessageKey : {kafkaMessage.GetMessageKey() as string}");

                       }, DistributedTracingData.TryDeserializeFromString(GetTracingData(data)));

            }
            catch (Exception ex) when (ex is not OrderReactorIdempotenceAlreadyExistingException)
            {
                logger.LogError(ex, "Exception when tring to check the idempotence of the Message see exception : {ex} and raw message content : {mess} / PROCESS ANYWAY", ex.ToString(), data?.Serialize());
            }
        }
            
        private static (string productReference, string category, string causationId) GetInformations(object data) => data switch
        {
            LegacyOrderCreatedMessage msg => (msg?.GetMessageKey(), null, msg?.CausationId),
            LegacyOrderAssignedMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderCancelledMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderDeliveryTimeUpdatedMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderDeliveryStatusUpdatedMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderDeliveryCostUpdatedMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderDeliveryDateUpdatedMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderCardMessageUpdatedMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderNotesUpdatedMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderDeliveryAddressUpdatedMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderDeliveredOnBehalfMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderAcceptedMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderRejectedMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderDeliveredMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderAssignationRemovedMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderAcceptedOnBehalfMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderRejectedOnBehalfMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderSentMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderRecipientNameUpdatedMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderRecipientPhoneNumberUpdatedMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderItemUpdatedMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),
            LegacyOrderRecipientCoordinatesUpdatedMessage msg => (msg?.Payload?.OrderIdentifier, null, msg?.CausationId),

            // France specific messages
            OrderPlacedMessage msg => (msg?.Payload?.OrderId, null, msg?.CausationId),
            OrderAssignmentMessage msg => (msg?.Payload?.OrderId, null, msg?.CausationId),
            OrderUpdatedMessage msg => (msg?.Payload?.OrderId, null, msg?.CausationId),
            OrderManagementStatusMessage msg => (msg?.Payload?.OrderId, null, msg?.CausationId),

            _ => (null, null, null),
        };

        private async Task<bool> NeedShutdownAsync(Exception e)
    => await featureManager.IsEnabled("ShutdownOnException");
    }
}
